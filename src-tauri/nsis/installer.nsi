; 基础设置
!define APP_NAME "问答精灵"
!define APP_VERSION "1.0.12"
!define APP_EXE "answer.exe"
!define APP_DIR "$PROGRAMFILES64\${APP_NAME}"

; 引入必要插件和库
!include "MUI2.nsh"          ; 现代界面库
!include "FileFunc.nsh"      ; 文件操作函数
!include "LogicLib.nsh"      ; 逻辑判断库

; 界面设置
!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

; 语言设置
!insertmacro MUI_LANGUAGE "ChineseSimplified"

; 安装部分（合并主程序和 WebView2 安装逻辑）
Section "主程序与依赖" SecMain
  SetOutPath "$INSTDIR"  ; 设置安装目录
  
  ; 1. 复制应用文件（包括 WebView2 安装程序）
  File /r "target\x86_64-pc-windows-gnu\release\*"  ; 复制编译产物
  File "src-tauri/bundle/windows/WebView2RuntimeInstaller.exe"  ; 复制 WebView2 安装程序
  
  ; 2. 检查 WebView2 是否已安装
  ${If} ${IsRegistryKey} "HKLM\SOFTWARE\WOW6432Node\Microsoft\EdgeUpdate\Clients\{********-FE2A-4295-8BDF-00C3A9A7E4C5}"
    ; 已安装，跳过
    DetailPrint "WebView2 运行时已安装"
  ${Else}
    ; 未安装，自动安装
    DetailPrint "检测到未安装 WebView2 运行时，开始自动安装..."
    ExecWait '"$INSTDIR\WebView2RuntimeInstaller.exe" /silent /install' $0  ; 静默安装
    ${If} $0 == 0
      DetailPrint "WebView2 安装成功"
    ${Else}
      MessageBox MB_ICONWARNING|MB_OK "WebView2 安装失败，可能影响应用运行。建议手动下载安装：https://go.microsoft.com/fwlink/p/?LinkId=2124703"
    ${EndIf}
    Delete "$INSTDIR\WebView2RuntimeInstaller.exe"  ; 安装完成后删除安装程序
  ${EndIf}
  
  ; 3. 创建桌面快捷方式
  CreateShortCut "$DESKTOP\${APP_NAME}.lnk" "$INSTDIR\${APP_EXE}" "" "$INSTDIR\${APP_EXE}" 0
  
  ; 标记组件为已安装
  SetInstDirRegKey HKLM "Software\${APP_NAME}" "InstallDir"
SectionEnd

; 卸载部分
Section "卸载" SecUninstall
  ; 1. 删除桌面快捷方式
  Delete "$DESKTOP\${APP_NAME}.lnk"
  
  ; 2. 删除安装目录
  RMDir /r "$INSTDIR"
  
  ; 3. 清理注册表
  DeleteRegKey HKLM "Software\${APP_NAME}"
SectionEnd

; 组件描述（显示在安装界面）
!insertmacro MUI_FUNCTION_DESCRIPTION_BEGIN
  !insertmacro MUI_DESCRIPTION_TEXT ${SecMain} "安装问答精灵应用及必要依赖（包括 WebView2 运行时）"
  !insertmacro MUI_DESCRIPTION_TEXT ${SecUninstall} "卸载问答精灵应用及相关文件"
!insertmacro MUI_FUNCTION_DESCRIPTION_END

; 安装包元信息
Name "${APP_NAME} ${APP_VERSION}"
OutFile "${APP_NAME}_${APP_VERSION}_x64-setup.exe"
InstallDir "${APP_DIR}"
InstallDirRegKey HKLM "Software\${APP_NAME}" "InstallDir"
ShowInstDetails show
ShowUnInstDetails show