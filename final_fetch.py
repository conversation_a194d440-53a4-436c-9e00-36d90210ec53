#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终版本 - 基于真实curl请求的Token Holders数据获取脚本
"""

import requests
import time

def fetch_token_holders():
    """获取所有Token持有者数据并保存到1.txt"""
    
    base_url = "https://www.oklink.com/api/explorer/v2/x1/tokens/holders/0x4cf55a735f45271548faed60340ef4658ccb167c"
    output_file = "1.txt"
    
    # 从你的curl命令中提取的关键头信息
    headers = {
        'accept': 'application/json',
        'accept-language': 'en,zh-CN;q=0.9,zh;q=0.8',
        'app-type': 'web',
        'devid': '5ad6aeac-3ed2-458d-9847-0b0ad35d52e5',
        'referer': 'https://www.oklink.com/zh-hans/x-layer/token/0x4cf55a735f45271548faed60340ef4658ccb167c?tab=holders',
        'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"macOS"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
        'x-apikey': 'LWIzMWUtNDU0Ny05Mjk5LWI2ZDA3Yjc2MzFhYmEyYzkwM2NjfDI4NjczMjM3NzAxNzY0OTg=',
        'x-cdn': 'https://static.oklink.com',
        'x-id-group': '2930162120255470001-c-40',
        'x-locale': 'zh_CN',
        'x-simulated-trading': 'undefined',
        'x-site-info': '9FjOikHdpRnblJCLiskTJx0SPJiOiUGZvNmIsICUKJiOi42bpdWZyJye',
        'x-utc': '8',
        'x-zkdex-env': '0',
        'ok-verify-token': '382dd3b8-e359-44de-92df-ec11163c27cb',
        'ok-verify-sign': 'RDaFOUASYs7b1GIIXZ1I/c3vcpWUFiGqXeeIdWKfFOI=',
    }
    
    # 关键cookies
    cookies = {
        'devId': '5ad6aeac-3ed2-458d-9847-0b0ad35d52e5',
        'locale': 'zh_CN',
        'fingerprint_id': '5ad6aeac-3ed2-458d-9847-0b0ad35d52e5',
        'traceId': '2930162120255470001',
        'ok-ses-id': 'ekk8F4TWONADsIrH9nUxJayX627TaTM5XvbtmGLbf6JBM9MUm9WAYnGVv/7z9uhTFO5Ze7pWOJl9eyAObUGpwaoUf8exGQcfEpDU9Z+0XwW2Nd7zAEpFdyAPdmylW6KX',
    }
    
    session = requests.Session()
    session.headers.update(headers)
    session.cookies.update(cookies)
    
    offset = 0
    limit = 100
    total_processed = 0
    is_first_write = True
    
    print("🚀 开始获取Token持有者数据...")
    
    while True:
        # 更新时间戳
        current_timestamp = int(time.time() * 1000)
        session.headers['ok-timestamp'] = str(current_timestamp)
        
        params = {
            'offset': offset,
            'limit': limit,
            'sort': 'value,desc',
            't': current_timestamp
        }
        
        try:
            print(f"📥 请求第 {offset // limit + 1} 页 (offset: {offset})...")
            
            response = session.get(base_url, params=params, timeout=30)
            
            if response.status_code != 200:
                print(f"❌ HTTP错误: {response.status_code}")
                print(f"响应: {response.text[:200]}")
                break
            
            data = response.json()
            
            if data.get('code') != 0:
                print(f"❌ API错误: {data.get('msg', '未知错误')}")
                break
            
            hits = data.get('data', {}).get('hits', [])
            total_count = data.get('data', {}).get('total', 0)
            
            print(f"📊 获取到 {len(hits)} 条记录，总计 {total_count} 条")
            
            if not hits:
                print("✅ 没有更多数据")
                break
            
            # 保存数据
            mode = 'w' if is_first_write else 'a'
            with open(output_file, mode, encoding='utf-8') as f:
                for item in hits:
                    token_address = item.get('tokenContractAddress', '')
                    value_change = item.get('valueChange24h', 0)
                    f.write(f"{token_address},{value_change}\n")
            
            total_processed += len(hits)
            print(f"✅ 已保存，累计 {total_processed} 条")
            
            # 检查是否完成
            if len(hits) < limit:
                print("📄 已到最后一页")
                break
            
            offset += limit
            is_first_write = False
            
            # 延迟
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ 错误: {e}")
            break
    
    print(f"\n🎉 完成！总共获取 {total_processed} 条记录")
    print(f"📁 已保存到: {output_file}")
    
    # 显示前几行内容
    try:
        with open(output_file, 'r') as f:
            lines = f.readlines()[:3]
            print(f"\n📄 文件内容示例:")
            for line in lines:
                print(f"  {line.strip()}")
    except:
        pass

if __name__ == "__main__":
    fetch_token_holders()
